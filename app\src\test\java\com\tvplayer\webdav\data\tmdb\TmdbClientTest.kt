package com.tvplayer.webdav.data.tmdb

import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mockito.*
import kotlinx.coroutines.runBlocking
import com.tvplayer.webdav.data.model.MediaItem
import com.tvplayer.webdav.data.model.MediaType
import com.tvplayer.webdav.data.model.Actor
import com.tvplayer.webdav.data.model.CrewMember
import java.lang.reflect.Method

/**
 * 测试 TmdbClient 的年份提取逻辑和详细数据缓存
 */
class TmdbClientTest {

    @Test
    fun testExtractTVYearAndTitle() {
        // 创建 TmdbClient 实例用于测试
        val mockApiService = mock(TmdbApiService::class.java)
        val tmdbClient = TmdbClient(mockApiService)
        
        // 使用反射访问私有方法
        val method: Method = TmdbClient::class.java.getDeclaredMethod("extractTVYearAndTitle", String::class.java)
        method.isAccessible = true

        // 测试用例：直接跟随年份
        var result = method.invoke(tmdbClient, "神话2025") as Pair<String, Int?>
        assertEquals("神话", result.first)
        assertEquals(2025, result.second)

        // 测试用例：点分隔年份
        result = method.invoke(tmdbClient, "神话.2025") as Pair<String, Int?>
        assertEquals("神话", result.first)
        assertEquals(2025, result.second)

        // 测试用例：英文括号年份
        result = method.invoke(tmdbClient, "神话(2025)") as Pair<String, Int?>
        assertEquals("神话", result.first)
        assertEquals(2025, result.second)

        // 测试用例：中文括号年份
        result = method.invoke(tmdbClient, "神话（2025）") as Pair<String, Int?>
        assertEquals("神话", result.first)
        assertEquals(2025, result.second)

        // 测试用例：带空格的括号年份
        result = method.invoke(tmdbClient, "神话 (2025)") as Pair<String, Int?>
        assertEquals("神话", result.first)
        assertEquals(2025, result.second)

        // 测试用例：方括号年份
        result = method.invoke(tmdbClient, "神话[2025]") as Pair<String, Int?>
        assertEquals("神话", result.first)
        assertEquals(2025, result.second)

        // 测试用例：没有年份
        result = method.invoke(tmdbClient, "神话") as Pair<String, Int?>
        assertEquals("神话", result.first)
        assertNull(result.second)

        // 测试用例：无效年份（超出范围）
        result = method.invoke(tmdbClient, "神话1800") as Pair<String, Int?>
        assertEquals("神话1800", result.first)
        assertNull(result.second)

        // 测试用例：无效年份（超出范围）
        result = method.invoke(tmdbClient, "神话2050") as Pair<String, Int?>
        assertEquals("神话2050", result.first)
        assertNull(result.second)

        // 测试用例：复杂标题
        result = method.invoke(tmdbClient, "权力的游戏.Game.of.Thrones.2011") as Pair<String, Int?>
        assertEquals("权力的游戏.Game.of.Thrones", result.first)
        assertEquals(2011, result.second)

        // 测试用例：英文标题
        result = method.invoke(tmdbClient, "Breaking Bad (2008)") as Pair<String, Int?>
        assertEquals("Breaking Bad", result.first)
        assertEquals(2008, result.second)
    }

    @Test
    fun testYearExtractionEdgeCases() {
        val mockApiService = mock(TmdbApiService::class.java)
        val tmdbClient = TmdbClient(mockApiService)
        
        val method: Method = TmdbClient::class.java.getDeclaredMethod("extractTVYearAndTitle", String::class.java)
        method.isAccessible = true

        // 测试边界年份
        var result = method.invoke(tmdbClient, "老剧1900") as Pair<String, Int?>
        assertEquals("老剧", result.first)
        assertEquals(1900, result.second)

        result = method.invoke(tmdbClient, "未来剧2030") as Pair<String, Int?>
        assertEquals("未来剧", result.first)
        assertEquals(2030, result.second)

        // 测试空字符串
        result = method.invoke(tmdbClient, "") as Pair<String, Int?>
        assertEquals("", result.first)
        assertNull(result.second)

        // 测试只有年份
        result = method.invoke(tmdbClient, "2025") as Pair<String, Int?>
        assertEquals("2025", result.first)
        assertNull(result.second)

        // 测试多个年份（应该匹配第一个模式）
        result = method.invoke(tmdbClient, "神话2025(2024)") as Pair<String, Int?>
        assertEquals("神话", result.first)
        assertEquals(2025, result.second)
    }

    @Test
    fun testMediaItemDetailedFields() {
        // 测试MediaItem是否正确包含详细信息字段
        val actors = listOf(
            Actor("1", "张三", "主角", "http://example.com/avatar1.jpg", false),
            Actor("2", "李四", "配角", "http://example.com/avatar2.jpg", false)
        )

        val crew = listOf(
            CrewMember("1", "王导演", "Director", "Directing", "http://example.com/director.jpg"),
            CrewMember("2", "赵编剧", "Writer", "Writing", null)
        )

        val mediaItem = MediaItem(
            id = "movie_123",
            title = "测试电影",
            originalTitle = "Test Movie",
            overview = "这是一个测试电影",
            mediaType = MediaType.MOVIE,
            filePath = "/test/movie.mp4",
            cast = actors,
            crew = crew,
            detailedOverview = "详细简介",
            productionCompanies = listOf("测试制片公司"),
            spokenLanguages = listOf("中文", "英文"),
            countries = listOf("中国", "美国"),
            budget = 1000000L,
            revenue = 5000000L,
            status = "Released"
        )

        // 验证基本信息
        assertEquals("movie_123", mediaItem.id)
        assertEquals("测试电影", mediaItem.title)
        assertEquals(MediaType.MOVIE, mediaItem.mediaType)

        // 验证详细信息
        assertEquals(2, mediaItem.cast.size)
        assertEquals("张三", mediaItem.cast[0].name)
        assertEquals("主角", mediaItem.cast[0].role)

        assertEquals(2, mediaItem.crew.size)
        assertEquals("王导演", mediaItem.crew[0].name)
        assertEquals("Director", mediaItem.crew[0].job)
        assertTrue(mediaItem.crew[0].isDirector())

        assertEquals("详细简介", mediaItem.detailedOverview)
        assertEquals(listOf("测试制片公司"), mediaItem.productionCompanies)
        assertEquals(1000000L, mediaItem.budget)
        assertEquals(5000000L, mediaItem.revenue)
        assertEquals("Released", mediaItem.status)
    }

    @Test
    fun testCrewMemberRoles() {
        val director = CrewMember("1", "导演", "Director", "Directing", null)
        val writer = CrewMember("2", "编剧", "Writer", "Writing", null)
        val producer = CrewMember("3", "制片人", "Producer", "Production", null)
        val screenwriter = CrewMember("4", "编剧", "Screenplay", "Writing", null)

        assertTrue(director.isDirector())
        assertFalse(director.isWriter())
        assertFalse(director.isProducer())

        assertFalse(writer.isDirector())
        assertTrue(writer.isWriter())
        assertFalse(writer.isProducer())

        assertFalse(producer.isDirector())
        assertFalse(producer.isWriter())
        assertTrue(producer.isProducer())

        assertFalse(screenwriter.isDirector())
        assertTrue(screenwriter.isWriter())
        assertFalse(screenwriter.isProducer())
    }

    @Test
    fun testTVSeriesDetailedFields() {
        val actors = listOf(
            Actor("1", "主演A", "角色A", null, false),
            Actor("2", "主演B", "角色B", null, false)
        )

        val tvEpisode = MediaItem(
            id = "tv_456_s1_e1",
            title = "第1集",
            originalTitle = "Episode 1",
            overview = "第一集简介",
            mediaType = MediaType.TV_EPISODE,
            filePath = "/test/series/s01e01.mp4",
            seasonNumber = 1,
            episodeNumber = 1,
            seriesId = "tv_456",
            seriesTitle = "测试电视剧",
            cast = actors,
            numberOfSeasons = 3,
            numberOfEpisodes = 30,
            status = "Ended",
            networks = listOf("测试电视台"),
            creators = listOf("创作者A", "创作者B")
        )

        // 验证电视剧特有字段
        assertEquals("tv_456_s1_e1", tvEpisode.id)
        assertEquals(MediaType.TV_EPISODE, tvEpisode.mediaType)
        assertEquals(1, tvEpisode.seasonNumber)
        assertEquals(1, tvEpisode.episodeNumber)
        assertEquals("tv_456", tvEpisode.seriesId)
        assertEquals("测试电视剧", tvEpisode.seriesTitle)

        // 验证详细信息
        assertEquals(2, tvEpisode.cast.size)
        assertEquals(3, tvEpisode.numberOfSeasons)
        assertEquals(30, tvEpisode.numberOfEpisodes)
        assertEquals("Ended", tvEpisode.status)
        assertEquals(listOf("测试电视台"), tvEpisode.networks)
        assertEquals(listOf("创作者A", "创作者B"), tvEpisode.creators)
    }
}
