package com.tvplayer.webdav.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * 媒体项目数据模型
 * 支持电影、电视剧、单集等不同类型
 */
@Parcelize
data class MediaItem(
    val id: String,
    val title: String,
    val originalTitle: String? = null,
    val overview: String? = null,
    val posterPath: String? = null,
    val backdropPath: String? = null,
    val releaseDate: Date? = null,
    val rating: Float = 0f,
    val duration: Long = 0L, // 时长（秒）
    val mediaType: MediaType,
    val filePath: String,
    val fileSize: Long = 0L,
    val lastModified: Date? = null,
    
    // 电视剧相关
    val seasonNumber: Int? = null,
    val episodeNumber: Int? = null,
    val seriesId: String? = null,
    val seriesTitle: String? = null,
    
    // 播放相关
    val watchedProgress: Float = 0f, // 观看进度 0-1
    val isWatched: Boolean = false,
    val lastWatchedTime: Date? = null,
    
    // 收藏和标签
    val isFavorite: Boolean = false,
    val tags: List<String> = emptyList(),
    val genre: List<String> = emptyList(),

    // 详细信息（用于详情页面，避免重复API调用）
    val cast: List<Actor> = emptyList(), // 演员信息
    val crew: List<CrewMember> = emptyList(), // 工作人员信息
    val detailedOverview: String? = null, // 详细简介（可能比overview更完整）
    val productionCompanies: List<String> = emptyList(), // 制作公司
    val spokenLanguages: List<String> = emptyList(), // 语言
    val countries: List<String> = emptyList(), // 制作国家
    val budget: Long? = null, // 预算（电影）
    val revenue: Long? = null, // 票房（电影）
    val numberOfSeasons: Int? = null, // 总季数（电视剧）
    val numberOfEpisodes: Int? = null, // 总集数（电视剧）
    val status: String? = null, // 状态（如：已完结、播出中等）
    val networks: List<String> = emptyList(), // 播出网络（电视剧）
    val creators: List<String> = emptyList() // 创作者（电视剧）
) : Parcelable {
    
    /**
     * 获取显示标题
     */
    fun getDisplayTitle(): String {
        fun deriveNameFromFile(): String {
            val base = filePath.substringAfterLast('/')
                .substringBeforeLast('.')
                .replace(Regex("[\\[\\(（【].*?[\\]\\)）】]"), " ")
                .replace(Regex("[._-]+"), " ")
                .replace(Regex("\\s+"), " ")
                .trim()
            return if (base.isNotBlank()) base else "未命名"
        }
        return when (mediaType) {
            MediaType.TV_EPISODE -> {
                val composite = if (seasonNumber != null && episodeNumber != null && !seriesTitle.isNullOrBlank()) {
                    "$seriesTitle S${seasonNumber.toString().padStart(2, '0')}E${episodeNumber.toString().padStart(2, '0')}"
                } else title
                (composite?.takeIf { it.isNotBlank() } ?: seriesTitle ?: deriveNameFromFile())
            }
            else -> (title.takeIf { it.isNotBlank() } ?: originalTitle?.takeIf { it.isNotBlank() } ?: deriveNameFromFile())
        }
    }
    
    /**
     * 获取副标题
     */
    fun getSubtitle(): String? {
        return when (mediaType) {
            MediaType.TV_EPISODE -> title.takeIf { it != getDisplayTitle() }
            MediaType.MOVIE -> {
                val dateStr = releaseDate?.let {
                    java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault()).format(it)
                }
                val durationStr = if (duration > 0) "${duration / 60}分钟" else null
                listOfNotNull(dateStr, durationStr).joinToString(" • ")
            }
            else -> null
        }
    }
    
    /**
     * 获取格式化的文件大小
     */
    fun getFormattedFileSize(): String {
        return when {
            fileSize < 1024 -> "${fileSize}B"
            fileSize < 1024 * 1024 -> "${fileSize / 1024}KB"
            fileSize < 1024 * 1024 * 1024 -> "${fileSize / (1024 * 1024)}MB"
            else -> String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0))
        }
    }
    
    /**
     * 获取观看进度百分比
     */
    fun getWatchedPercentage(): Int {
        return (watchedProgress * 100).toInt()
    }
    
    /**
     * 是否为新内容（最近添加）
     */
    fun isNew(): Boolean {
        val now = System.currentTimeMillis()
        val weekAgo = now - 7 * 24 * 60 * 60 * 1000L
        return lastModified?.time ?: 0L > weekAgo
    }
}

/**
 * 媒体类型枚举
 */
enum class MediaType {
    MOVIE,          // 电影
    TV_SERIES,      // 电视剧（系列）
    TV_EPISODE,     // 电视剧单集
    DOCUMENTARY,    // 纪录片
    ANIMATION,      // 动画
    OTHER           // 其他
}

/**
 * 电视剧系列汇总数据模型
 * 用于在首页显示电视剧系列而不是单个剧集
 */
@Parcelize
data class TVSeriesSummary(
    val seriesId: String,
    val seriesTitle: String,
    val posterPath: String? = null,
    val backdropPath: String? = null,
    val overview: String? = null,
    val rating: Float = 0f,
    val releaseDate: Date? = null,
    val genre: List<String> = emptyList(),
    val totalSeasons: Int = 0,
    val totalEpisodes: Int = 0,
    val watchedEpisodes: Int = 0,
    val lastWatchedTime: Date? = null,
    val episodes: List<MediaItem> = emptyList() // 包含的所有剧集
) : Parcelable {

    /**
     * 获取显示的副标题（季数和集数信息）
     */
    fun getSubtitle(): String {
        return if (totalSeasons > 0) {
            "${totalSeasons}季 · ${totalEpisodes}集"
        } else {
            "${totalEpisodes}集"
        }
    }

    /**
     * 获取观看进度
     */
    fun getWatchedProgress(): Float {
        return if (totalEpisodes > 0) {
            watchedEpisodes.toFloat() / totalEpisodes.toFloat()
        } else 0f
    }

    /**
     * 是否有观看进度
     */
    fun hasWatchedProgress(): Boolean {
        return watchedEpisodes > 0
    }
}

/**
 * 媒体分类
 */
data class MediaCategory(
    val id: String,
    val name: String,
    val description: String? = null,
    val iconRes: Int? = null,
    val mediaType: MediaType? = null,
    val itemCount: Int = 0
)
