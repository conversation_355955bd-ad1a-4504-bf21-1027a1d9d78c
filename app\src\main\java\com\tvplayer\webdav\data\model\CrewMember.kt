package com.tvplayer.webdav.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 工作人员数据模型
 * 用于存储导演、编剧、制片人等工作人员信息
 */
@Parcelize
data class CrewMember(
    val id: String,
    val name: String,
    val job: String, // 职位，如：Director, Writer, Producer
    val department: String, // 部门，如：Directing, Writing, Production
    val profilePath: String? = null // 头像路径
) : Parcelable {
    
    /**
     * 是否为导演
     */
    fun isDirector(): <PERSON><PERSON>an {
        return job.equals("Director", ignoreCase = true)
    }
    
    /**
     * 是否为编剧
     */
    fun isWriter(): Boolean {
        return job.contains("Writer", ignoreCase = true) || 
               job.contains("Screenplay", ignoreCase = true) ||
               job.contains("Story", ignoreCase = true)
    }
    
    /**
     * 是否为制片人
     */
    fun isProducer(): Boolean {
        return job.contains("Producer", ignoreCase = true)
    }
    
    /**
     * 获取头像完整URL
     */
    fun getAvatarUrl(): String? {
        return profilePath?.let { 
            "${com.tvplayer.webdav.data.tmdb.TmdbApiService.IMAGE_BASE_URL}${com.tvplayer.webdav.data.tmdb.TmdbApiService.POSTER_SIZE_W500}$it" 
        }
    }
}
